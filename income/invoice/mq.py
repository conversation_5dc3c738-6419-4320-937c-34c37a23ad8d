from decimal import Decimal

import pandas as pd
from django.db import transaction

from income import const
from income.charge.models import IncomeChargeDetail
from income.customer.models import IncomeAccountSeq
from income.invoice.models import IncomeInvoice
from income.invoice.models import IncomeInvoiceDetail


class InvoiceBatchIssuance(object):  # noqa: UP004
    """
    批量预开票
    开票规则:
        查出来的数据按界⾯条件筛选后,按照分账序号分组,
        累加明细数据⾦额 (权责⾦额-已开票⾦额)

        操作⼊表income_invoice表:
            amount是累加的明细数据⾦额
            tax为分账序号对应的tax
            tax_amount = amount - (amount ÷ (1+tax/100),
            currency_type/invoice_currency_type的值随机挑选⼀条权责的currency_type
            exchange_rate置空,

        操作⼊表income_invoice_detail表:
            invoice_id是上步⼊表的id
            target_id是权责数据的id
            target_type是bill
            amount = 每条的权责⾦额-已开票⾦额
            invoice_month置空
    """

    def __init__(self, data: dict):
        self.task_id = data["task_id"]
        self.start_charge_month = data["start_charge_month"]
        self.end_charge_month = data["end_charge_month"]
        self.customer_num = data.get("customer_num")
        self.account_seq = data.get("account_seq")
        self.create_user = data["create_user"]

    def get_queryset(self):
        sql = """
        SELECT
            a.`id`,
            a.`account_seq`,
            a.`fee_amount`,
            a.`currency_type`,
            a.`customer_num`,
            a.`charge_month`,
            COALESCE(d.`invoice_amount`, 0) AS invoice_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN (
                SELECT
                    `target_id`,
                    sum(`amount`) AS invoice_amount
                FROM
                    `income_invoice_detail`
                WHERE
                    target_type = 'bill'
                GROUP BY
                    `target_id`) d ON a.id = d.`target_id`
        WHERE
            a.`pay_type` = '预付'
            AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
            AND a.`need_invoice` = 1
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    def handle_queryset(self, queryset):
        """处理查询结果集,按条件筛选并分组汇总数据"""
        charge_df = pd.DataFrame([row.__dict__ for row in queryset])
        charge_df = charge_df.drop(columns=["_state"])

        # 如果没有数据,直接返回
        if charge_df.empty:
            return

        # 1. 对账单周期进行筛选
        charge_df = charge_df[
            (charge_df["charge_month"] >= self.start_charge_month)
            & (charge_df["charge_month"] <= self.end_charge_month)
        ]
        # 2. 如果customer_num有值,先对charge_df筛选
        if self.customer_num:
            charge_df = charge_df[charge_df["customer_num"] == self.customer_num]
        # 3. 如果account_seq有值,同时增加customer_num和account_seq的筛选
        if self.account_seq:
            charge_df = charge_df[charge_df["account_seq"] == self.account_seq]

        # 如果筛选后没有数据,直接返回
        if charge_df.empty:
            return

        charge_df["available_amount"] = (
            charge_df["fee_amount"] - charge_df["invoice_amount"]
        )

        # 过滤掉可开票金额为0或负数的记录
        charge_df = charge_df[charge_df["available_amount"] > 0]
        # 剔除account_seq为空的记录
        charge_df = charge_df[
            charge_df["account_seq"].notna() & charge_df["account_seq"].ne("")
        ]
        if charge_df.empty:
            return

        # 按分账序号分组汇总
        grouped = (
            charge_df.groupby("account_seq")
            .agg(
                {
                    "available_amount": "sum",  # 累加明细数据金额
                    "currency_type": "first",  # 随机挑选一条权责的currency_type
                    "id": list,  # 收集所有相关的权责记录ID
                    "fee_amount": list,  # 收集所有权责金额
                    "invoice_amount": list,  # 收集所有已开票金额
                    "customer_num": "first",  # 收集客户编号
                },
            )
            .reset_index()
        )

        # 批量处理每个分账序号的开票数据
        self._process_invoice_data(grouped, charge_df)

    def _get_account_seq_tax_map(self, grouped_df):
        """获取分账序号对应的税率"""
        res = IncomeAccountSeq.objects.filter(
            account_seq__in=grouped_df["account_seq"],
        ).values("account_seq", "tax")
        return dict(res)

    def _process_invoice_data(self, grouped_df, original_df):
        """处理发票数据入库"""
        invoice_objects = []
        invoice_detail_objects = []
        account_seq_tax_map = self._get_account_seq_tax_map(grouped_df)
        for _, row in grouped_df.iterrows():
            account_seq = row["account_seq"]
            total_amount = row["available_amount"]
            currency_type = row["currency_type"]
            charge_detail_ids = row["id"]
            fee_amounts = row["fee_amount"]
            invoice_amounts = row["invoice_amount"]
            customer_num = row["customer_num"]
            # 获取分账序号对应的税率
            tax_rate = account_seq_tax_map.get(account_seq, 0)
            # 计算税额: tax_amount = amount - (amount ÷ (1 + tax/100))
            tax_amount = (
                total_amount - (total_amount / (1 + tax_rate / 100))
                if tax_rate > 0
                else Decimal("0.00")
            )
            # 生成发票号
            invoice_no = self._generate_invoice_no(account_seq)
            # 创建发票记录
            invoice = IncomeInvoice(
                invoice_no=invoice_no,
                amount=total_amount,
                account_seq=account_seq,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                currency_type=currency_type,
                invoice_currency_type=currency_type,
                exchange_rate=None,  # 置空
                customer_num=customer_num,
                create_user=self.create_user,
            )
            invoice_objects.append(invoice)

            # 为每个权责记录创建发票明细
            for i, charge_detail_id in enumerate(charge_detail_ids):
                detail_amount = fee_amounts[i] - invoice_amounts[i]
                if detail_amount > 0:  # 只处理有可开票金额的记录
                    detail = IncomeInvoiceDetail(
                        target_id=charge_detail_id,
                        target_type=const.InvoiceTargetType.BILL,
                        amount=detail_amount,
                    )
                    invoice_detail_objects.append(detail)

        # 使用事务批量插入数据
        with transaction.atomic():
            # 批量创建发票记录
            created_invoices = IncomeInvoice.objects.bulk_create(
                invoice_objects,
                batch_size=100,
            )

            # 为发票明细设置invoice_id
            invoice_id_mapping = {}
            for i, invoice in enumerate(created_invoices):
                invoice_id_mapping[invoice_objects[i].account_seq] = invoice.id

            # 设置发票明细的invoice_id
            for detail in invoice_detail_objects:
                # 根据target_id找到对应的account_seq
                charge_detail = original_df[original_df["id"] == detail.target_id]
                if not charge_detail.empty:
                    account_seq = charge_detail["account_seq"].iloc[0]
                    detail.invoice_id = invoice_id_mapping.get(account_seq)

            # 批量创建发票明细记录
            IncomeInvoiceDetail.objects.bulk_create(
                invoice_detail_objects,
                batch_size=500,
            )

    def _generate_invoice_no(self, account_seq):
        """生成发票号"""
        import time

        return f"INV#{int(time.time() * 1000)}"

    def run(self):
        """执行批量开票任务"""
        queryset = self.get_queryset()
        self.handle_queryset(queryset)
