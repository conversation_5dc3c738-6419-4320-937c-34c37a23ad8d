from rest_framework import serializers

from income import message
from income.charge.models import IncomeChargeDetail


class PostPaidInvoiceSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调整账期")
    invoice_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="开票金额",
    )

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "sub_order_no",
            "account_seq",
            "charge_month",
            "fee_amount",
            "tax",
            "tax_type",
            "income_type",
            "pay_type",
            "customer_name",
            "adjust_month",
            "invoice_amount",
        )


class PostPaidBatchInvoiceIssuanceSerializer(serializers.Serializer):
    # 客户编号和分账序号可不选择
    customer_num = serializers.CharField(
        help_text="客户编号",
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    account_seq = serializers.CharField(
        help_text="分账序号",
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    # 账期区间, 必填
    start_charge_month = serializers.IntegerField(help_text="起始账期", required=True)
    end_charge_month = serializers.IntegerField(help_text="结束账期", required=True)

    def validate(self, attrs):
        if attrs["start_charge_month"] > attrs["end_charge_month"]:
            raise serializers.ValidationError(
                {"start_charge_month": message.INVALID_CHARGE_MONTH_RANGE},
            )
        return attrs
