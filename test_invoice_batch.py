#!/usr/bin/env python
"""
测试批量开票功能的简单脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/Users/<USER>/income-project')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from income.invoice.mq import InvoiceBatchIssuance


def test_invoice_batch_issuance():
    """测试批量开票功能"""
    
    # 测试数据
    test_data = {
        "task_id": "test-task-001",
        "start_charge_month": 202401,
        "end_charge_month": 202412,
        "customer_num": "CUST001",  # 可以为None
        "account_seq": "FZ-**********",  # 可以为None
    }
    
    print("开始测试批量开票功能...")
    print(f"测试数据: {test_data}")
    
    try:
        # 创建批量开票实例
        batch_invoice = InvoiceBatchIssuance(test_data)
        
        # 获取查询结果
        print("\n1. 获取查询结果...")
        queryset = batch_invoice.get_queryset()
        print(f"查询到 {len(list(queryset))} 条记录")
        
        # 重新获取queryset（因为上面已经消费了）
        queryset = batch_invoice.get_queryset()
        
        # 处理查询结果
        print("\n2. 处理查询结果...")
        batch_invoice.handle_queryset(queryset)
        
        print("\n✅ 批量开票测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_with_different_conditions():
    """测试不同条件下的批量开票"""
    
    test_cases = [
        {
            "name": "仅指定客户编号",
            "data": {
                "task_id": "test-task-002",
                "start_charge_month": 202401,
                "end_charge_month": 202412,
                "customer_num": "CUST001",
                "account_seq": None,
            }
        },
        {
            "name": "仅指定分账序号",
            "data": {
                "task_id": "test-task-003",
                "start_charge_month": 202401,
                "end_charge_month": 202412,
                "customer_num": None,
                "account_seq": "FZ-**********",
            }
        },
        {
            "name": "不指定任何筛选条件",
            "data": {
                "task_id": "test-task-004",
                "start_charge_month": 202401,
                "end_charge_month": 202412,
                "customer_num": None,
                "account_seq": None,
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{'='*50}")
        print(f"测试场景: {test_case['name']}")
        print(f"测试数据: {test_case['data']}")
        
        try:
            batch_invoice = InvoiceBatchIssuance(test_case['data'])
            queryset = batch_invoice.get_queryset()
            batch_invoice.handle_queryset(queryset)
            print(f"✅ {test_case['name']} 测试通过")
        except Exception as e:
            print(f"❌ {test_case['name']} 测试失败: {str(e)}")


if __name__ == "__main__":
    print("批量开票功能测试")
    print("="*50)
    
    # 基本功能测试
    test_invoice_batch_issuance()
    
    # 不同条件测试
    test_with_different_conditions()
    
    print("\n" + "="*50)
    print("所有测试完成!")
